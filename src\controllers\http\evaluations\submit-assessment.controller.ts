import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage  } from '@tess-f/backend-utils'
import { EventType } from '@tess-f/shared-config'
import submitAssessment from '../../../services/internal/submit-assessment-service.js'
import { SubmitAssessmentRequest } from '../../../models/internal/submit-assessment.js'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z, ZodError } from 'zod'
import httpStatus from 'http-status'
import getEvaluationSession from '../../../services/mssql/session/get.service.js'
import updateEvaluationSession from '../../../services/mssql/session/update.service.js'
import { SessionModel } from '../../../models/session.model.js'

const { BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.Submit-Assessment', httpLogTransformer)

/**
 * HTTP controller for submitting completed assessments.
 * 
 * Handles POST
 * Expected request body:
 * - sessionId: uuid found in request path
 * - responses: QuestionResponse[] - Array of student responses
 * 
 * Returns:
 * - 200: Successful submission with scoring results
 * - 400: Invalid request data
 * - 500: Server error during processing
 */
export default async function submitAssessmentController(
  req: Request,
  res: Response
): Promise<void> {
  
  //TODO ZOD PARSE
  // const responses = req.body.responses
  //
  try {
    const sessionId = z.object({ id: zodGUID }).parse(req.params)
    const session = await getEvaluationSession(sessionId.id)
    const endTime = new Date()
  
    userResponses = z.array(checklistUserResponseSchema).parse(req.body)
    
    log('info', 'Received assessment submission request', {
      request: req,
      sessionId:sessionId.id,
      responsesCount: req.body?.responses?.length || 0,
      eventType: EventType.evaluation_grade
    })

    //make sure to save session with end time 
    // aftrer save the close response and continue
    //send session data back pending score 0
    // Extract request data
    const submissionRequest: SubmitAssessmentRequest = {
      sessionId: sessionId.id,
      responses: req.body.responses || [],
      startTime: session.fields.Start!.toISOString(),
      endTime: endTime.toISOString()
    }
    const sessionUpdate: SessionModel = new SessionModel({
      Id: sessionId.id,
      End: endTime,
      Score: 0,
      Passed: false
    })

    // need to save session 
    await updateEvaluationSession(sessionUpdate)

    // Return success with just 
    res.status(httpStatus.OK).json({
      success: true,
      data: submissionRequest,
      message: 'Assessment submitted for grading successfully'
    })

    // Process the submission
    const result = await submitAssessment(submissionRequest)

    log('info', 'Assessment submission completed successfully', {
      sessionId: result.sessionId,
      passed: result.passed,
      totalScore: result.totalScore,
      maxScore: result.maxScore,
      responsesCount: result.responsesCount,
      eventType: EventType.evaluation_create
    })

  } catch (error) {
    const errorMessage = getErrorMessage(error)
    
    log('error', 'Failed to submit assessment', {
      sessionId: req.body?.sessionId,
      error: errorMessage,
      eventType: EventType.evaluation_create
    })
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', {
        errorMessage: zodErrorToMessage(error),
        success: false,
        sessionId: req.params.id,
        req,
        eventType: EventType.input_validation_errors
      })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
       // Determine error status code
      let statusCode = 500
      if (errorMessage.includes('Invalid request data')) {
        statusCode = 400
      } else if (errorMessage.includes('not found')) {
        statusCode = 404
      }

      res.status(httpStatus).json({
        success: false,
        error: errorMessage,
        message: 'Failed to submit assessment'
      })
    }
  }
}
